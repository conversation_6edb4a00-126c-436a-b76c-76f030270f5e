import {
  Lightbulb,
  Users,
  PackageCheck,
  Globe,
  Phone,
  Mail,
  MapPin,
} from "lucide-react";
import img from "../../assets/images/workerImg.webp";
import { useEffect, useState, useMemo } from "react";
import { useStatisticsStore } from "../../store";

// Static data constants
const FEATURE_CARDS = [
  {
    icon: <Lightbulb className="text-[var(--primary)] bg-white rounded-full p-2 w-10 h-10" />,
    title: "20 Years Of Experience",
    desc: "Over 20 years in the production of high-quality electric air pumps.",
  },
  {
    icon: <Users className="text-[var(--primary)] bg-white rounded-full p-2 w-10 h-10" />,
    title: "R&D & Production Team",
    desc: "Skilled design and production team for innovative air pump solutions.",
  },
  {
    icon: <PackageCheck className="text-[var(--primary)] bg-white rounded-full p-2 w-10 h-10" />,
    title: "Advanced Facilities",
    desc: "State-of-the-art manufacturing facilities and quality control systems.",
  },
  {
    icon: <Globe className="text-[var(--primary)] bg-white rounded-full p-2 w-10 h-10" />,
    title: "Global Pricing Advantage",
    desc: "Competitive pricing with flexible payment methods worldwide.",
  },
];

const CONTACT_INFO = [
  { icon: Phone, text: "+971-123-456-789", label: "Phone number" },
  { icon: Mail, text: "<EMAIL>", label: "Email address" },
  { icon: MapPin, text: "Damascus, Syria", label: "Office location" },
];

export default function ContactSection() {
  const { StatisticsData, isLoading, error, fetchStatisticsData } = useStatisticsStore();
  
  
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: '',
    country: '',
    message: ''
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState('');

  useEffect(() => {
    fetchStatisticsData();
  }, [fetchStatisticsData]);

  // Memoized statistics data
  const statisticsItems = useMemo(() => [
    {
      number: StatisticsData?.project_number || '0',
      label: "Global Projects In 2024",
      icon: <Globe size={30} className="text-[var(--primary)]" />,
    },
    {
      number: StatisticsData?.customer_number || '0',
      label: "Customers Worldwide",
      icon: <PackageCheck size={30} className="text-[var(--primary)]" />,
    },
    {
      number: StatisticsData?.team_number || '0',
      label: "Professional Team Members",
      icon: <Users size={30} className="text-[var(--primary)]" />,
    },
    {
      number: StatisticsData?.experience_number || '0',
      label: "Years Of Experience",
      icon: <Lightbulb size={30} className="text-[var(--primary)]" />,
    },
  ], [StatisticsData]);

  // Form handlers
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitMessage('');

    try {
      // Simulate API call - replace with actual submission logic
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Reset form on success
      setFormData({
        name: '',
        phone: '',
        email: '',
        country: '',
        message: ''
      });
      
      setSubmitMessage('Thank you! Your inquiry has been sent successfully.');
    } catch {
      setSubmitMessage('Sorry, there was an error sending your inquiry. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section className="relative z-0 font-sans bg-white">
      {/* Background Section with overlay */}
      <div
        className="pt-20 pb-80 px-6 md:px-20 text-white relative z-0"
        style={{
          backgroundImage: `linear-gradient(rgba(206, 22, 27, 0.85), rgba(206, 22, 27, 0.85)), url(${img})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
        role="banner"
      >
        <div className="max-w-screen-xl mx-auto flex flex-col lg:flex-row lg:items-center lg:justify-between text-center lg:text-left gap-4">
          <h1 className="text-3xl md:text-4xl font-bold text-white">
            Premium Electric Air Pump Solutions at Competitive Prices
          </h1>
          <p className="text-base md:text-lg max-w-2xl text-white lg:max-w-xl lg:ml-10">
            Pyramid Power delivers premium electric air pumps and custom
            manufacturing services to clients worldwide, ensuring reliable
            performance and exceptional quality.
          </p>
        </div>

        {/* Feature Cards */}
        <div className="max-w-screen-xl mx-auto mt-14 grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {FEATURE_CARDS.map((item, idx) => (
            <div
              key={idx}
              className="bg-white text-black rounded-lg p-6 shadow-md relative overflow-hidden transition-transform duration-300 transform hover:-translate-y-2 hover:ring-2 hover:ring-[var(--primary)] group"
              role="article"
            >
              <div aria-hidden="true">{item.icon}</div>
              <h3 className="font-semibold mt-6 mb-2 text-lg min-h-[48px]">
                {item.title}
              </h3>
              <p className="text-sm text-gray-600 leading-relaxed min-h-[60px]">
                {item.desc}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Inquiry Form + Details */}
      <div className="relative z-10 -mt-48 ">
        <div className="bg-white rounded-xl shadow-xl max-w-screen-xl mx-auto px-6 md:px-12 py-12 grid md:grid-cols-2 gap-10">
          {/* Left side: info text and contact icons */}
          <div>
            <h2 className="text-2xl font-bold text-[#1c1a30] mb-3">
              Send An Inquiry
            </h2>
            <p className="text-sm text-gray-600 mb-6">
              If you're looking for electric air pumps, compressors, or custom
              pneumatic solutions, reach out and our team will reply within 48
              hours.
            </p>
            <div className="border-t border-gray-300 pt-6 space-y-4">
              {CONTACT_INFO.map((contact, idx) => (
                <div key={idx} className="flex items-center gap-4 text-sm text-gray-600">
                  <contact.icon 
                    className="text-[var(--primary)]" 
                    size={18} 
                    aria-label={contact.label}
                  />
                  <span>{contact.text}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Right side: form */}
          <form 
            className="grid grid-cols-1 sm:grid-cols-2 gap-6 "
            onSubmit={handleSubmit}
            aria-label="Contact inquiry form"
          >
            <input
              type="text"
              name="name"
              placeholder="Name"
              value={formData.name}
              onChange={handleChange}
              className="input input-bordered w-full "
              required
              aria-label="Your name"
              disabled={isSubmitting}
            />
            <input
              type="tel"
              name="phone"
              placeholder="Phone"
              value={formData.phone}
              onChange={handleChange}
              className="input input-bordered w-full"
              required
              aria-label="Your phone number"
              disabled={isSubmitting}
            />
            <input
              type="email"
              name="email"
              placeholder="Email"
              value={formData.email}
              onChange={handleChange}
              className="input input-bordered w-full"
              required
              aria-label="Your email address"
              disabled={isSubmitting}
            />
            <input
              type="text"
              name="country"
              placeholder="Country"
              value={formData.country}
              onChange={handleChange}
              className="input input-bordered w-full"
              required
              aria-label="Your country"
              disabled={isSubmitting}
            />
            <textarea
              name="message"
              placeholder="Please describe your needs..."
              rows={4}
              value={formData.message}
              onChange={handleChange}
              className="w-full textarea textarea-bordered sm:col-span-2"
              required
              aria-label="Your message"
              disabled={isSubmitting}
            />
            
            {submitMessage && (
              <div className={`sm:col-span-2 p-3 rounded text-sm ${
                submitMessage.includes('error') || submitMessage.includes('Sorry')
                  ? 'bg-red-100 text-red-700 border border-red-300'
                  : 'bg-green-100 text-green-700 border border-green-300'
              }`}>
                {submitMessage}
              </div>
            )}
            
            <button 
              type="submit"
              className="btn bg-[var(--primary)] text-white hover:bg-[#b01419] sm:col-span-2 disabled:opacity-50"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Sending...' : 'Contact Us'}
            </button>
          </form>
        </div>
      </div>

      {/* Stats */}
      <div className="mt-28 pb-20 px-6 md:px-20 ">
        <div className="max-w-screen-xl mx-auto">
          {isLoading ? (
            <div className="text-center py-12">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary)]"></div>
              <p className="mt-4 text-gray-600">Loading statistics...</p>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-red-500">Failed to load statistics</p>
              <button 
                onClick={fetchStatisticsData}
                className="mt-2 text-[var(--primary)] hover:underline"
              >
                Try again
              </button>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
              {statisticsItems.map((stat, idx) => (
                <div
                  key={idx}
                  className="flex items-center gap-4 p-4 bg-white rounded-lg shadow hover:shadow-md transition"
                  role="article"
                >
                  <div 
                    className="bg-[var(--primary)]/10 rounded-full w-14 h-14 flex items-center justify-center shrink-0"
                    aria-hidden="true"
                  >
                    {stat.icon}
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-[#1c1a30]">
                      {stat.number}
                    </h3>
                    <p className="text-sm text-gray-600">{stat.label}</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </section>
  );
}
