import {
  Zap,
  Shield,
  Gauge,
  <PERSON><PERSON>s,
  Award,
  CheckCircle,
  Star,
  TrendingUp,
  Wrench,
  Target,
  Clock,
  Battery,
  Thermometer,
  Wind,
  Volume2,
  Cpu,
  Trophy,
  Rocket,
  Globe,
} from "lucide-react";

export default function ProductFeatures({ product }) {
  const features = product.features;

  // Icon mapping for different feature types
  const getFeatureIcon = (feature, index) => {
    const featureText = (
      typeof feature === "string"
        ? feature
        : feature.title || feature.name || ""
    ).toLowerCase();

    // Smart icon selection based on feature content
    if (
      featureText.includes("power") ||
      featureText.includes("energy") ||
      featureText.includes("electric")
    ) {
      return <Zap className="w-6 h-6" />;
    } else if (
      featureText.includes("safe") ||
      featureText.includes("protect") ||
      featureText.includes("security")
    ) {
      return <Shield className="w-6 h-6" />;
    } else if (
      featureText.includes("speed") ||
      featureText.includes("fast") ||
      featureText.includes("quick")
    ) {
      return <Gauge className="w-6 h-6" />;
    } else if (
      featureText.includes("control") ||
      featureText.includes("adjust") ||
      featureText.includes("setting")
    ) {
      return <Settings className="w-6 h-6" />;
    } else if (
      featureText.includes("quality") ||
      featureText.includes("premium") ||
      featureText.includes("award")
    ) {
      return <Award className="w-6 h-6" />;
    } else if (
      featureText.includes("reliable") ||
      featureText.includes("durable") ||
      featureText.includes("stable")
    ) {
      return <CheckCircle className="w-6 h-6" />;
    } else if (
      featureText.includes("performance") ||
      featureText.includes("efficient") ||
      featureText.includes("optimize")
    ) {
      return <TrendingUp className="w-6 h-6" />;
    } else if (
      featureText.includes("maintain") ||
      featureText.includes("service") ||
      featureText.includes("repair")
    ) {
      return <Wrench className="w-6 h-6" />;
    } else if (
      featureText.includes("precise") ||
      featureText.includes("accurate") ||
      featureText.includes("target")
    ) {
      return <Target className="w-6 h-6" />;
    } else if (
      featureText.includes("time") ||
      featureText.includes("timer") ||
      featureText.includes("schedule")
    ) {
      return <Clock className="w-6 h-6" />;
    } else if (
      featureText.includes("battery") ||
      featureText.includes("charge") ||
      featureText.includes("portable")
    ) {
      return <Battery className="w-6 h-6" />;
    } else if (
      featureText.includes("temperature") ||
      featureText.includes("heat") ||
      featureText.includes("cool")
    ) {
      return <Thermometer className="w-6 h-6" />;
    } else if (
      featureText.includes("air") ||
      featureText.includes("flow") ||
      featureText.includes("ventil")
    ) {
      return <Wind className="w-6 h-6" />;
    } else if (
      featureText.includes("quiet") ||
      featureText.includes("silent") ||
      featureText.includes("noise")
    ) {
      return <Volume2 className="w-6 h-6" />;
    } else if (
      featureText.includes("smart") ||
      featureText.includes("digital") ||
      featureText.includes("auto")
    ) {
      return <Cpu className="w-6 h-6" />;
    } else {
      // Fallback icons based on index
      const fallbackIcons = [
        <Star className="w-6 h-6" />,
        <Trophy className="w-6 h-6" />,
        <Rocket className="w-6 h-6" />,
        <Globe className="w-6 h-6" />,
      ];
      return fallbackIcons[index % fallbackIcons.length];
    }
  };

  return (
    <section className="bg-slate-200 border-t border-gray-200 px-4 sm:px-6 md:px-10 lg:px-20 py-16">
      <div className="max-w-7xl mx-auto">
        {/* Enhanced Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-3 mb-4">
            <div className="w-12 h-12 bg-[var(--primary)] rounded-xl flex items-center justify-center">
              <Star className="w-6 h-6 text-white" />
            </div>
            <h2 className="text-4xl font-bold text-gray-900">Key Features</h2>
          </div>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Discover the advanced capabilities that make this product stand out
            in the market
          </p>
          <div className="w-24 h-1 bg-[var(--primary)] rounded mx-auto mt-4"></div>
        </div>

        {/* Enhanced Features Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
          {features.map((feature, idx) => (
            <div
              key={idx}
              className="group bg-white rounded-2xl p-8 shadow-lg border border-gray-100 hover:shadow-xl hover:border-gray-200 transition-all duration-300 transform hover:-translate-y-1"
            >
              <div className="flex items-start gap-6">
                {/* Enhanced Icon */}
                <div className="flex-shrink-0">
                  <div className="relative">
                    <div className="w-16 h-16 bg-gradient-to-br from-[var(--primary)] to-red-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                      <div className="text-white">
                        {getFeatureIcon(feature, idx)}
                      </div>
                    </div>
                    <div className="absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-xs">
                        {idx + 1}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Enhanced Content */}
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-gray-900 mb-3 group-hover:text-[var(--primary)] transition-colors duration-300">
                    {typeof feature === "string"
                      ? feature
                      : feature.title || feature.name}
                  </h3>
                  <p className="text-gray-600 text-base leading-relaxed">
                    {typeof feature === "string"
                      ? "This advanced feature enhances product performance, reliability, and user experience through innovative engineering and design."
                      : feature.description ||
                        "This feature provides enhanced functionality and improved performance for optimal user experience."}
                  </p>
                  <div className="mt-4 flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span className="text-sm font-medium text-green-700">
                      Verified Feature
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Enhanced Feature Summary */}
        <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-2">
              Why Choose This Product?
            </h3>
            <p className="text-gray-600">
              Built with excellence, designed for performance
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                icon: <Trophy className="w-8 h-8" />,
                title: "Industry Leading",
                desc: "Best-in-class performance standards with cutting-edge technology",
                color: "from-yellow-400 to-orange-500",
              },
              {
                icon: <Shield className="w-8 h-8" />,
                title: "Proven Reliability",
                desc: "Rigorously tested in demanding environments for maximum durability",
                color: "from-green-400 to-emerald-500",
              },
              {
                icon: <Rocket className="w-8 h-8" />,
                title: "Future Ready",
                desc: "Advanced engineering built to meet tomorrow's challenges",
                color: "from-blue-400 to-indigo-500",
              },
            ].map((summary, idx) => (
              <div key={idx} className="text-center group">
                <div
                  className={`w-16 h-16 bg-gradient-to-br ${summary.color} rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:shadow-xl transition-shadow duration-300`}
                >
                  <div className="text-white">{summary.icon}</div>
                </div>
                <h4 className="text-xl font-bold text-gray-900 mb-3">
                  {summary.title}
                </h4>
                <p className="text-gray-600 leading-relaxed">{summary.desc}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
