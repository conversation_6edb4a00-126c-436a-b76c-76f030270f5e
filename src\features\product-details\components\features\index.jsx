export default function ProductFeatures({ product }) {
  const features = product.features;



  return (
    <section className="bg-slate-200 border-t border-gray-200 px-6 md:px-20 py-12">
      <div className="max-w-6xl mx-auto">
        {/* Simple Header */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            Key Features
          </h2>
          <div className="w-16 h-1 bg-[var(--primary)] rounded"></div>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {features.map((feature, idx) => (
            <div
              key={idx}
              className="bg-white rounded-lg p-6 shadow-sm border border-gray-200 hover:shadow-md transition duration-200"
            >
              <div className="flex items-start gap-4">
                {/* Feature Number */}
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-[var(--primary)] rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold text-sm">
                      {String(idx + 1).padStart(2, '0')}
                    </span>
                  </div>
                </div>

                {/* Feature Content */}
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">
                    {typeof feature === 'string' ? feature : feature.title || feature.name}
                  </h3>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {typeof feature === 'string' 
                      ? "This feature enhances product performance and reliability." 
                      : feature.description || "Feature description not available."}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Feature Summary - Simple */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[
            { title: "Industry Leading", desc: "Best-in-class performance standards" },
            { title: "Proven Reliability", desc: "Tested in demanding environments" },
            { title: "Future Ready", desc: "Built for tomorrow's challenges" }
          ].map((summary, idx) => (
            <div key={idx} className="bg-white rounded-lg p-4 text-center border border-gray-200">
              <h4 className="text-lg font-bold text-gray-900 mb-2">{summary.title}</h4>
              <p className="text-gray-600 text-sm">{summary.desc}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
