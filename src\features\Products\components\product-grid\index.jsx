import { Package } from "lucide-react";
import ProductCard from "../product-card";
import SkeletonCard from "../skeleton-card";
import { useProductsStore } from "../../../../store/productsApi";

export default function ProductGrid({ products, isLoading, isSearchMode = false }) {
  // Use passed isLoading prop or fallback to store state
  const storeIsLoading = useProductsStore((state) => state.isLoading);
  const selectedCategory = useProductsStore((state) => state.selectedCategory);
  const loading = isLoading !== undefined ? isLoading : storeIsLoading;

  return (
    <div>
      {/* Results Header */}
      <div className="flex items-center justify-between mb-6">
        <p className="text-sm text-gray-600">
          {loading
            ? isSearchMode ? "Searching..." : "Loading products..."
            : `Showing ${products.length} ${products.length === 1 ? "product" : "products"}`}
        </p>
      </div>

      {/* Products Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-6">
        {loading
          ? Array.from({ length: 6 }).map((_, i) => <SkeletonCard key={i} />)
          : products.map((product) => (
              <ProductCard key={product.id} product={product} category={selectedCategory} />
            ))}
      </div>

      {/* Empty State */}
      {!loading && products.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Package size={64} className="mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {isSearchMode ? "No search results found" : "No products found"}
          </h3>
          <p className="text-gray-600">
            {isSearchMode 
              ? "Try searching with different keywords." 
              : "Try adjusting your filters to see more results."}
          </p>
        </div>
      )}
    </div>
  );
}