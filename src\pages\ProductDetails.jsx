import { useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import Footer from "../layout/footer";
import ProductInfo from "../features/product-details/components/product-info";
import ProductFeatures from "../features/product-details/components/features";
import WorkingPrinciple from "../features/product-details/components/working-principle";
import TechnicalSpecifications from "../features/product-details/components/technical-specifications";
import ProductDescription from "../features/product-details/components/product-description";
import RelatedProducts from "../features/product-details/components/related-products";
import useProductDetailsStore from "../store/productDetailsApi";


export default function ProductDetails() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { product, loading, error, fetchProductDetails, clearProduct } = useProductDetailsStore();

  useEffect(() => {
    if (id) {
      fetchProductDetails(id);
    }
    
    return () => {
      clearProduct();
    };
  }, [id, fetchProductDetails, clearProduct]);

  const handleBack = () => {
    navigate(-1);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-[var(--primary)] border-t-transparent mx-auto"></div>
          <p className="mt-4 text-gray-700 font-semibold text-base">
            Loading product details...
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center"> 
          <div className="text-red-500 text-6xl mb-4">⚠</div>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Error Loading Product</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button 
            onClick={() => navigate('/products')}
            className="bg-[var(--primary)] text-white px-6 py-2 rounded-lg hover:bg-opacity-90 transition"
          >
            Back to Products
          </button>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Product Not Found</h2>
          <p className="text-gray-600 mb-4">The requested product could not be found.</p>
          <button 
            onClick={() => navigate('/products')}
            className="bg-[var(--primary)] text-white px-6 py-2 rounded-lg hover:bg-opacity-90 transition"
          >
            Back to Products
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <main className="flex-grow mx-auto w-full">
        <ProductInfo product={product} onBack={handleBack} />
        <ProductDescription product={product} />
        <ProductFeatures product={product} />
        <WorkingPrinciple product={product} />
        <TechnicalSpecifications product={product} />
        <RelatedProducts product={product} />
      </main>
      <Footer />
    </div>
  );
}
