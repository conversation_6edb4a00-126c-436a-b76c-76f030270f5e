@import "tailwindcss";
@plugin "daisyui";

:root {
  --navbar-height: 4rem; /* Adjust this value to match your actual navbar height */
  --primary: #ce161b; /* Primary brand color */
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}
/* Custom Swiper Pagination Styles */
.hero-swiper .swiper-pagination {
  bottom: 1rem !important;
  z-index: 50 !important;
}

.hero-swiper .swiper-pagination-bullet {
  width: 8px !important;
  height: 4px !important;
  border-radius: 9999px !important;
  background: rgba(255, 255, 255, 0.4) !important;
  opacity: 1 !important;
  transition: all 0.3s ease !important;
  margin: 0 4px !important;
}

.hero-swiper .swiper-pagination-bullet:hover {
  background: rgba(255, 255, 255, 0.6) !important;
  transform: scale(1.25) !important;
}

.hero-swiper .swiper-pagination-bullet-active {
  background: white !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

@media (min-width: 768px) {
  .hero-swiper .swiper-pagination {
    bottom: 1.5rem !important;
  }
  .hero-swiper .swiper-pagination-bullet {
    width: 12px !important;
    height: 6px !important;
    margin: 0 6px !important;
  }
}

@media (min-width: 1024px) {
  .hero-swiper .swiper-pagination {
    bottom: 2rem !important;
  }
  .hero-swiper .swiper-pagination-bullet {
    width: 16px !important;
    height: 8px !important;
    margin: 0 6px !important;
  }
}

.products-swiper .swiper-pagination {
  position: relative !important;
  margin-top: 2rem !important;
  /* bottom: auto !important; */
}

.products-swiper .swiper-pagination-bullet {
  width: 8px !important;
  height: 4px !important;
  border-radius: 9999px !important;
  background: #9ca3af !important;
  opacity: 1 !important;
  transition: all 0.3s ease !important;
  margin: 0 4px !important;
}

.products-swiper .swiper-pagination-bullet:hover {
  background: #6b7280 !important;
  transform: scale(1.25) !important;
}

.products-swiper .swiper-pagination-bullet-active {
  background: var(--primary) !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

@media (min-width: 768px) {
  .products-swiper .swiper-pagination-bullet {
    width: 12px !important;
    height: 6px !important;
    margin: 0 6px !important;
  }
}

@media (min-width: 1024px) {
  .products-swiper .swiper-pagination-bullet {
    width: 16px !important;
    height: 8px !important;
    margin: 0 6px !important;
  }
}

/* Set Poppins as the default font family for the entire project */
* {
  font-family: "Poppins", sans-serif;
}

body {
  font-family: "Poppins", sans-serif;
  color:black;
  background-color: white; 
}

/* Line clamp utilities for text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
