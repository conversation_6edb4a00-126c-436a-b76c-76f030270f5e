import axios from 'axios';

// Create axios instance with base configuration
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    console.log('Making request to:', config.baseURL + config.url);
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    // Properly detect Axios cancellations and silently ignore
    if (axios.isCancel(error)) {
      // Cancellation is expected, no error log needed
      return Promise.reject(error);
    }
    // Also check AbortError for fetch cancellations
    if (error.name === 'AbortError') {
      return Promise.reject(error);
    }

    // Log any other errors
    console.error('API Error:', error.response?.data || error.message);

    return Promise.reject(error);
  }
);

export default apiClient;
