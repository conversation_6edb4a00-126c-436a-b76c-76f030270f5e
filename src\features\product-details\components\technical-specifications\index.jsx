export default function TechnicalSpecifications({ product }) {
  const comparisonTable = product.comparison_table;

  if (!comparisonTable || comparisonTable.length === 0) {
    return null;
  }

  // Get all unique specification fields from all models
  const allSpecFields = [];
  comparisonTable.forEach(model => {
    if (model.specs) {
      model.specs.forEach(spec => {
        if (!allSpecFields.some(field => field.field === spec.field)) {
          allSpecFields.push(spec);
        }
      });
    }
  });

  return (
    <section className="bg-white border-t border-gray-200 px-6 md:px-20 py-12">
      <div className="max-w-7xl mx-auto">
        {/* Simple Header */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            Technical Specifications
          </h2>
          <p className="text-gray-600 mb-4">
            Compare detailed specifications across all available product models
          </p>
          <div className="w-16 h-1 bg-[var(--primary)] rounded"></div>
        </div>

        {/* Table Container */}
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          {/* Table Header Info */}
          <div className="bg-[var(--primary)] px-6 py-4">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between">
              <div>
                <h3 className="text-xl font-bold text-white mb-1">Model Comparison</h3>
                <p className="text-red-100 text-sm">{comparisonTable.length} models available</p>
              </div>
              <div className="flex items-center gap-4 text-red-100 text-xs mt-2 md:mt-0">
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span>High Stock</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                  <span>Low Stock</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                  <span>Out of Stock</span>
                </div>
              </div>
            </div>
          </div>

          {/* Scrollable Table */}
          <div className="overflow-x-auto">
            <table className="w-full text-sm border-collapse min-w-max">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left font-semibold text-gray-900 sticky left-0 bg-gray-50 border-r border-gray-200">
                    Model
                  </th>
                  <th className="px-4 py-3 text-left font-semibold text-gray-900 border-r border-gray-200">
                    SKU
                  </th>
                  <th className="px-4 py-3 text-left font-semibold text-gray-900 border-r border-gray-200">
                    Stock
                  </th>
                  <th className="px-4 py-3 text-left font-semibold text-gray-900 border-r border-gray-200">
                    Price
                  </th>
                  {allSpecFields.map((spec, index) => (
                    <th key={index} className="px-4 py-3 text-center font-semibold text-gray-900 whitespace-nowrap border-r border-gray-200 last:border-r-0">
                      <div>
                        <div className="font-bold">{spec.field}</div>
                        {spec.unit && <div className="text-xs text-gray-500 mt-1">({spec.unit})</div>}
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {comparisonTable.map((model, modelIndex) => (
                  <tr
                    key={modelIndex}
                    className={`hover:bg-gray-50 ${
                      modelIndex % 2 === 0 ? "bg-white" : "bg-gray-25"
                    }`}
                  >
                    <td className="px-4 py-4 border-b border-gray-200 font-semibold text-gray-900 sticky left-0 bg-inherit border-r border-gray-200">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-[var(--primary)] rounded-full"></div>
                        {model.label}
                      </div>
                    </td>
                    <td className="px-4 py-4 border-b border-gray-200 text-gray-600 text-xs border-r border-gray-200">
                      <span className="bg-gray-100 px-2 py-1 rounded text-xs">{model.sku}</span>
                    </td>
                    <td className="px-4 py-4 border-b border-gray-200 border-r border-gray-200">
                      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded text-xs font-medium ${
                        model.stock_quantity > 10 
                          ? 'bg-green-100 text-green-800' 
                          : model.stock_quantity > 0 
                          ? 'bg-yellow-100 text-yellow-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        <div className={`w-1.5 h-1.5 rounded-full ${
                          model.stock_quantity > 10 ? 'bg-green-400' : 
                          model.stock_quantity > 0 ? 'bg-yellow-400' : 'bg-red-400'
                        }`}></div>
                        {model.stock_quantity}
                      </span>
                    </td>
                    <td className="px-4 py-4 border-b border-gray-200 font-semibold text-[var(--primary)] border-r border-gray-200">
                      {model.price ? `$${model.price}` : 'Contact'}
                    </td>
                    {allSpecFields.map((specField, specIndex) => {
                      const modelSpec = model.specs?.find(s => s.field === specField.field);
                      return (
                        <td
                          key={specIndex}
                          className="px-4 py-4 border-b border-gray-200 text-center whitespace-nowrap border-r border-gray-200 last:border-r-0"
                        >
                          {modelSpec?.value ? (
                            <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs font-medium">
                              {modelSpec.value}
                            </span>
                          ) : (
                            <span className="text-gray-400 text-xs">N/A</span>
                          )}
                        </td>
                      );
                    })}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Table Footer */}
          <div className="bg-gray-50 px-6 py-3 border-t border-gray-200">
            <p className="text-sm text-gray-600">
              Scroll horizontally to view all specifications • {comparisonTable.length} models available
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
