import {
  Info,
  ShieldCheck,
  Zap,
  GaugeCircle,
} from "lucide-react";

export default function ProductDescription({ product }) {
  if (!product.description) return null;

  const highlights = [
    {
      icon: <ShieldCheck className="w-6 h-6 text-green-600" />,
      title: "Industrial Grade",
      desc: "Built for heavy-duty applications.",
    },
    {
      icon: <Zap className="w-6 h-6 text-yellow-500" />,
      title: "Energy Efficient",
      desc: "Optimized for low power consumption.",
    },
    {
      icon: <GaugeCircle className="w-6 h-6 text-blue-600" />,
      title: "Reliable Performance",
      desc: "Consistent operation under load.",
    },
  ];

  return (
    <section className="border-t border-gray-200 px-6 md:px-20 py-16">
      <div className="max-w-5xl mx-auto">
        {/* Section Header */}
        <div className="mb-10">
          <div className="flex items-center gap-3 mb-3">
            <Info className="w-6 h-6 text-[var(--primary)]" />
            <h2 className="text-3xl font-bold text-gray-900">
              Product Description
            </h2>
          </div>
          <div className="w-20 h-1 bg-[var(--primary)] rounded"></div>
        </div>

        {/* Description Box */}
        <div className="bg-slate-100  rounded-xl p-8 shadow-md leading-relaxed text-gray-800 text-[17px] prose max-w-none prose-p:mb-4 prose-headings:mb-3">
          {/* Optional: Format text into paragraphs if it's plain string */}
          {product.description.split("\n").map((para, index) => (
            <p key={index}>{para.trim()}</p>
          ))}
        </div>

        {/* Key Highlights */}
        <div className="mt-12">
          <h3 className="text-2xl font-bold text-gray-800 mb-6">
            Key Highlights
          </h3>
          <div className="grid gap-6 md:grid-cols-3">
            {highlights.map((item, index) => (
              <div
                key={index}
                className="bg-slate-100 p-5 rounded-xl shadow-sm text-center flex flex-col items-center transition hover:shadow-md"
              >
                <div className="mb-3">{item.icon}</div>
                <h4 className="font-semibold text-gray-900 mb-1 text-lg">{item.title}</h4>
                <p className="text-gray-600 text-sm">{item.desc}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
