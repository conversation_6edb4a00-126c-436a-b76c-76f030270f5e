import { useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import { useProductsStore } from "../store/productsApi";
import { useSearchStore } from "../store/searchApi";
import ProductFilter from "../features/Products/components/product-filter";
import ProductGrid from "../features/Products/components/product-grid";
import ProductsPagination from "../features/Products/components/products-pagination";

export default function Products() {
  const [searchParams] = useSearchParams();
  const searchQuery = searchParams.get("search") || "";

  const {
    products,
    smartFetchProducts,
    currentPage,
    totalPages,
    perPage,
    isLoading,
  } = useProductsStore();

  const {
    searchResults,
    searchProducts,
    clearSearch,
    isSearching,
  } = useSearchStore();

  // Determine if we're showing search results or regular products
  const isSearchMode = Boolean(searchQuery);
  const displayProducts = isSearchMode ? searchResults : products;
  const displayLoading = isSearchMode ? isSearching : isLoading;

  // Fetch products or search results on mount and when search query changes
  useEffect(() => {
    if (isSearchMode) {
      searchProducts(searchQuery);
    } else {
      clearSearch();
      smartFetchProducts();
    }
  }, [searchQuery, isSearchMode, searchProducts, clearSearch, smartFetchProducts]);

  return (
    <div className="container mx-auto px-4 py-10 grid grid-cols-1 lg:grid-cols-4 gap-8">
      {/* Sidebar Filters - Hide when searching */}
      {!isSearchMode && (
        <aside className="lg:col-span-1">
          <ProductFilter />
        </aside>
      )}

      {/* Product List + Pagination */}
      <main className={`space-y-8 ${isSearchMode ? "lg:col-span-4" : "lg:col-span-3"}`}>
        {/* Search Results Header */}
        {isSearchMode && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h2 className="text-lg font-semibold text-blue-900 mb-2">
              Search Results for "{searchQuery}"
            </h2>
            <p className="text-blue-700 text-sm">
              {displayLoading
                ? "Searching..."
                : `Found ${searchResults.length} ${searchResults.length === 1 ? "product" : "products"}`}
            </p>
          </div>
        )}

        <ProductGrid products={displayProducts} isLoading={displayLoading} isSearchMode={isSearchMode} />

        {/* Only show pagination for regular products, not search results */}
        {!isSearchMode && (
          <ProductsPagination currentPage={currentPage} totalPages={totalPages} perPage={perPage} />
        )}
      </main>
    </div>
  );
}
